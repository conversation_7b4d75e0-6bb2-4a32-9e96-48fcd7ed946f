<?php
/**
 * The template for displaying all pages
 * 
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 */

get_header();
?>

<main id="primary" class="main">
    <div class="container">
        
        <?php while (have_posts()) : the_post(); ?>
            
            <article id="post-<?php the_ID(); ?>" <?php post_class('page-content'); ?>>
                
                <!-- Page Header -->
                <header class="page-header u-text-center u-mb-xl">
                    <?php if (has_post_thumbnail()) : ?>
                        <div class="page-header__image u-mb-lg">
                            <?php the_post_thumbnail('large', array('class' => 'page-header__featured-image')); ?>
                        </div>
                    <?php endif; ?>
                    
                    <h1 class="page-title"><?php the_title(); ?></h1>
                    
                </header><!-- .page-header -->
                
                <!-- Page Content -->
                <div class="page-content__body">
                    <div class="entry-content">
                        <?php
                        the_content();
                        
                        wp_link_pages(array(
                            'before' => '<div class="page-links u-text-center u-mt-lg">',
                            'after'  => '</div>',
                            'link_before' => '<span class="page-link">',
                            'link_after'  => '</span>',
                        ));
                        ?>
                    </div><!-- .entry-content -->
                </div><!-- .page-content__body -->
                
                <!-- Page Footer -->
                <footer class="page-footer u-mt-xl">
                    <?php if (get_edit_post_link()) : ?>
                        <div class="page-footer__edit u-text-center">
                            <?php
                            edit_post_link(
                                sprintf(
                                    wp_kses(
                                        /* translators: %s: Name of current post. Only visible to screen readers */
                                        __('Edit <span class="screen-reader-text">%s</span>', 'odd'),
                                        array(
                                            'span' => array(
                                                'class' => array(),
                                            ),
                                        )
                                    ),
                                    get_the_title()
                                ),
                                '<span class="edit-link">',
                                '</span>'
                            );
                            ?>
                        </div>
                    <?php endif; ?>
                </footer><!-- .page-footer -->
                
            </article><!-- #post-<?php the_ID(); ?> -->
            
            <?php
            // If comments are open or we have at least one comment, load up the comment template.
            if (comments_open() || get_comments_number()) :
                ?>
                <div class="page-comments u-mt-xl">
                    <?php comments_template(); ?>
                </div>
                <?php
            endif;
            ?>
            
        <?php endwhile; // End of the loop. ?>
        
    </div><!-- .container -->
</main><!-- #primary -->

<?php
get_footer();
